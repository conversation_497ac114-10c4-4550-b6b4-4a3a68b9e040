'use client';

import InstructorEmptyState from '@/components/partner/instructor/InstructorEmptyState';
import InstructorListItemComponent from '@/components/partner/instructor/InstructorListItem';
import { Button } from '@/components/ui/button';
import { instructorApi } from '@/lib/api/partner/instructor.api';
import { useSuspenseQuery } from '@tanstack/react-query';
import { Plus } from 'lucide-react';
import { Suspense, useState } from 'react';
import { toast } from 'sonner';
import DeleteInstructorModal from '@/components/partner/instructor/DeleteInstructorModal';
import { InstructorListItem } from '../../_types/client.type';

function InstructorClientPageContent() {
  const { data: instructors, refetch } = useSuspenseQuery({
    queryKey: ['instructors'],
    queryFn: () => instructorApi.getInstructors(),
  });

  // 삭제 확인 모달 상태
  const [deleteState, setDeleteState] = useState<{
    instructor: InstructorListItem | null;
    isDeleting: boolean;
  }>({ instructor: null, isDeleting: false });

  // 강사 수정
  const handleEdit = (instructor: InstructorListItem) => {
    // TODO: 강사 수정 페이지로 이동
    toast.info(`${instructor.name} 강사 정보 수정 기능은 준비 중입니다.`);
  };

  const handleDelete = (instructor: InstructorListItem) => {
    setDeleteState({ instructor, isDeleting: false });
  };

  const handleConfirmDelete = async () => {
    if (!deleteState.instructor) return;
    setDeleteState(prev => ({ ...prev, isDeleting: true }));

    try {
      await instructorApi.disableInstructor(deleteState.instructor.id);
      toast.success(`${deleteState.instructor.name} 강사가 삭제되었습니다.`);
      setDeleteState({ instructor: null, isDeleting: false });
      refetch();
    } catch (error) {
      console.error('handleConfirmDelete error', error);
      toast.error('강사 삭제 중 오류가 발생했습니다.');
      setDeleteState(prev => ({ ...prev, isDeleting: false }));
    }
  };

  // 강사 등록 버튼 클릭
  const handleAddInstructor = () => {
    // 강사 등록 페이지로 이동
    window.location.href = '/partner/instructor/new';
  };

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* 헤더 */}
      <div className='mb-8 flex items-center justify-between'>
        <div>
          <h1 className='mb-2 text-2xl font-bold text-gray-900'>소속 강사</h1>
          <p className='text-sm text-gray-600'>
            {instructors.length > 0
              ? `총 ${instructors.length}명의 강사가 등록되어 있습니다.`
              : '등록된 강사가 없습니다.'}
          </p>
        </div>

        <div className='flex items-center gap-3'>
          <Button
            onClick={handleAddInstructor}
            className='flex items-center gap-2'
          >
            <Plus className='h-4 w-4' />
            강사 등록
          </Button>
        </div>
      </div>

      {/* 콘텐츠 */}
      {instructors.length === 0 ? (
        <InstructorEmptyState onAddInstructor={handleAddInstructor} />
      ) : (
        <div className='space-y-4'>
          {instructors.map(instructor => (
            <InstructorListItemComponent
              key={instructor.id}
              instructor={{
                id: instructor.id,
                name: instructor.name,
                profileImageUrl: instructor.profileImages?.[0]?.url ?? '',
              }}
              onEdit={handleEdit}
              onDelete={handleDelete}
            />
          ))}
        </div>
      )}

      <DeleteInstructorModal
        isOpen={!!deleteState.instructor}
        onClose={() => setDeleteState({ instructor: null, isDeleting: false })}
        onConfirm={handleConfirmDelete}
        instructor={deleteState.instructor}
        isLoading={deleteState.isDeleting}
      />
    </div>
  );
}

export default function InstructorClientPage() {
  return (
    <Suspense fallback={<div></div>}>
      <InstructorClientPageContent />
    </Suspense>
  );
}
